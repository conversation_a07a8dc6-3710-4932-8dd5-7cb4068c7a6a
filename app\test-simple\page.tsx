"use client";

import React, { useState } from 'react';

export default function TestSimplePage() {
  const [currentPage, setCurrentPage] = useState(1);
  const [users] = useState([
    { id: 1, name: 'User 1' },
    { id: 2, name: 'User 2' },
    { id: 3, name: 'User 3' },
    { id: 4, name: 'User 4' },
    { id: 5, name: 'User 5' },
    { id: 6, name: 'User 6' },
    { id: 7, name: 'User 7' },
    { id: 8, name: 'User 8' },
    { id: 9, name: 'User 9' },
    { id: 10, name: 'User 10' },
    { id: 11, name: 'User 11' },
  ]);

  const itemsPerPage = 5;
  const totalPages = Math.ceil(users.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentUsers = users.slice(startIndex, endIndex);

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>Test Simple Pagination</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <p>Current Page: <strong>{currentPage}</strong></p>
        <p>Total Pages: <strong>{totalPages}</strong></p>
        <p>Showing: {startIndex + 1}-{Math.min(endIndex, users.length)} of {users.length}</p>
      </div>

      {/* Users List */}
      <div style={{ marginBottom: '20px' }}>
        <h2>Users on Page {currentPage}:</h2>
        <ul>
          {currentUsers.map(user => (
            <li key={user.id} style={{ padding: '5px 0' }}>
              {user.name}
            </li>
          ))}
        </ul>
      </div>

      {/* Pagination */}
      <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
        <button
          style={{
            padding: '8px 16px',
            backgroundColor: currentPage <= 1 ? '#ccc' : '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: currentPage <= 1 ? 'not-allowed' : 'pointer'
          }}
          disabled={currentPage <= 1}
          onMouseDown={() => {
            console.log('Previous mousedown');
            if (currentPage > 1) {
              setCurrentPage(currentPage - 1);
            }
          }}
          onClick={() => {
            console.log('Previous click');
            if (currentPage > 1) {
              setCurrentPage(currentPage - 1);
            }
          }}
        >
          Previous
        </button>

        {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
          <button
            key={page}
            style={{
              padding: '8px 12px',
              backgroundColor: page === currentPage ? '#007bff' : '#f8f9fa',
              color: page === currentPage ? 'white' : '#333',
              border: '1px solid #dee2e6',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
            onMouseDown={() => {
              console.log('Page mousedown:', page);
              setCurrentPage(page);
            }}
            onClick={() => {
              console.log('Page click:', page);
              setCurrentPage(page);
            }}
          >
            {page}
          </button>
        ))}

        <button
          style={{
            padding: '8px 16px',
            backgroundColor: currentPage >= totalPages ? '#ccc' : '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: currentPage >= totalPages ? 'not-allowed' : 'pointer'
          }}
          disabled={currentPage >= totalPages}
          onMouseDown={() => {
            console.log('Next mousedown');
            if (currentPage < totalPages) {
              setCurrentPage(currentPage + 1);
            }
          }}
          onClick={() => {
            console.log('Next click');
            if (currentPage < totalPages) {
              setCurrentPage(currentPage + 1);
            }
          }}
        >
          Next
        </button>
      </div>

      {/* Alternative with different events */}
      <div style={{ marginTop: '40px' }}>
        <h2>Alternative Events:</h2>
        <div style={{ display: 'flex', gap: '10px' }}>
          <button
            style={{ padding: '8px 16px', backgroundColor: '#28a745', color: 'white', border: 'none', borderRadius: '4px' }}
            onPointerDown={() => {
              console.log('Pointer down - go to page 1');
              setCurrentPage(1);
            }}
          >
            Go to Page 1 (PointerDown)
          </button>
          
          <button
            style={{ padding: '8px 16px', backgroundColor: '#dc3545', color: 'white', border: 'none', borderRadius: '4px' }}
            onTouchStart={(e) => {
              e.preventDefault();
              console.log('Touch start - go to page 2');
              setCurrentPage(2);
            }}
          >
            Go to Page 2 (TouchStart)
          </button>
          
          <div
            style={{ 
              padding: '8px 16px', 
              backgroundColor: '#6f42c1', 
              color: 'white', 
              borderRadius: '4px',
              cursor: 'pointer',
              userSelect: 'none'
            }}
            onMouseDown={() => {
              console.log('Div mousedown - go to page 3');
              setCurrentPage(3);
            }}
          >
            Go to Page 3 (Div)
          </div>
        </div>
      </div>
    </div>
  );
}
