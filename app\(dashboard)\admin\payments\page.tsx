import { prisma } from "@/lib/config/prisma";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import Link from "next/link";
import { formatCurrency } from "@/lib/utils/format";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table";
import { But<PERSON> } from "@/app/components/ui/button";
import { Badge } from "@/app/components/ui/badge";
import { Search } from "lucide-react";
import { PaymentActionsMenu } from "@/app/components/admin/payment-actions-menu";
import { Input } from "@/app/components/ui/input";
import { PaymentStatus } from "@prisma/client";

// Pastikan halaman selalu up-to-date
export const dynamic = 'force-dynamic';

// Fungsi untuk mendapatkan tampilan status pembayaran
function getPaymentStatusBadge(status: string) {
  switch (status.toUpperCase()) {
    case "FULLY_PAID":
      return { label: "Lunas", variant: "default" as const, color: "bg-green-100 text-green-800" };
    case "DEPOSIT_PAID":
      return { label: "Deposit Dibayar", variant: "success" as const, color: "bg-blue-100 text-blue-800" };
    case "DEPOSIT_PENDING":
      return { label: "Menunggu Deposit", variant: "secondary" as const, color: "bg-yellow-100 text-yellow-800" };
    case "FAILED":
      return { label: "Gagal", variant: "destructive" as const, color: "bg-red-100 text-red-800" };
    default:
      return { label: "Menunggu", variant: "outline" as const, color: "bg-gray-100 text-gray-800" };
  }
}

export default async function AdminPaymentsPage({
  searchParams
}: {
  searchParams: { q?: string, status?: string }
}) {
  const session = await auth();

  // Redirect ke login jika tidak ada session atau bukan admin
  if (!session?.user || session.user.role !== "ADMIN") {
    redirect('/login');
  }

  // Ambil parameter pencarian dan filter status
  const params = await Promise.resolve(searchParams);
  const searchQuery = params.q || '';
  const statusFilter = params.status || '';

  // Siapkan filter untuk status
  const statusWhere = {
    status: statusFilter ? statusFilter as PaymentStatus : undefined
  };

  // Ambil data pembayaran dari database
  type PaymentWithRelations = {
    id: string;
    amount: number;
    deposit: number;
    remaining: number;
    overtime: number | null;
    status: PaymentStatus;
    transactionId: string | null;
    createdAt: Date;
    updatedAt: Date;
    rental: {
      operationalEnd: Date | null;
      user: {
        name: string;
        phone: string;
      };
      product: {
        name: string;
        capacity: number;
      };
    };
  };

  const payments = await prisma.payment.findMany({
    where: {
      ...statusWhere,
      OR: [
        { rental: { user: { name: { contains: searchQuery, mode: 'insensitive' } } } },
        { rental: { user: { email: { contains: searchQuery, mode: 'insensitive' } } } },
        { rental: { user: { phone: { contains: searchQuery, mode: 'insensitive' } } } },
        { rental: { product: { name: { contains: searchQuery, mode: 'insensitive' } } } },
      ]
    },
    include: {
      rental: {
        include: {
          product: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true
            }
          }
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    }
  }) as PaymentWithRelations[];

  // Menghitung jumlah pembayaran berdasarkan status
  const depositPendingCount = payments.filter(p => p.status === 'DEPOSIT_PENDING').length;
  const depositPaidCount = payments.filter(p => p.status === 'DEPOSIT_PAID').length;
  const fullyPaidCount = payments.filter(p => p.status === 'FULLY_PAID').length;
  const failedCount = payments.filter(p => p.status === 'FAILED').length;

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Manajemen Pembayaran</h1>
        <div className="flex items-center gap-2">
          <form className="relative" action="/admin/payments">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              name="q"
              placeholder="Cari pembayaran..."
              className="pl-9 w-[250px]"
              defaultValue={searchQuery}
            />
            {statusFilter && (
              <input type="hidden" name="status" value={statusFilter} />
            )}
          </form>
        </div>
      </div>

      <div className="flex flex-wrap gap-2 mb-6">
        <Link href="/admin/payments">
          <Button variant={!statusFilter ? "default" : "outline"} size="sm">
            Semua ({payments.length})
          </Button>
        </Link>
        <Link href="/admin/payments?status=DEPOSIT_PENDING">
          <Button variant={statusFilter === 'DEPOSIT_PENDING' ? "default" : "outline"} size="sm" className="bg-yellow-600 hover:bg-yellow-700">
            Menunggu Deposit ({depositPendingCount})
          </Button>
        </Link>
        <Link href="/admin/payments?status=DEPOSIT_PAID">
          <Button variant={statusFilter === 'DEPOSIT_PAID' ? "default" : "outline"} size="sm" className="bg-blue-600 hover:bg-blue-700">
            Deposit Dibayar ({depositPaidCount})
          </Button>
        </Link>
        <Link href="/admin/payments?status=FULLY_PAID">
          <Button variant={statusFilter === 'FULLY_PAID' ? "default" : "outline"} size="sm" className="bg-green-600 hover:bg-green-700">
            Lunas ({fullyPaidCount})
          </Button>
        </Link>
        <Link href="/admin/payments?status=FAILED">
          <Button variant={statusFilter === 'FAILED' ? "default" : "outline"} size="sm" className="bg-red-600 hover:bg-red-700">
            Gagal ({failedCount})
          </Button>
        </Link>
      </div>

      <Card className="border-border shadow-sm">
        <CardHeader className="border-b border-border bg-muted/50">
          <CardTitle className="text-foreground">Daftar Pembayaran</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="border-b border-border bg-muted/30 hover:bg-muted/50">
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">ID</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Pelanggan</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Produk</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Total</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Deposit (50%)</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Sisa (50%)</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Overtime</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Status</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium w-[60px]">Aksi</TableHead>
                </TableRow>
              </TableHeader>
            <TableBody>
              {payments.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-12 text-muted-foreground">
                    Tidak ada data pembayaran
                  </TableCell>
                </TableRow>
              ) : (
                payments.map((payment) => {
                  const statusBadge = getPaymentStatusBadge(payment.status);
                  const hasOvertime = payment.overtime && payment.overtime > 0;
                  const isPending = payment.status === "DEPOSIT_PENDING";
                  const isDepositPaid = payment.status === "DEPOSIT_PAID";
                  const isCompleted = payment.rental.operationalEnd != null;
                  const needsRemainingPayment = isDepositPaid && isCompleted;

                  return (
                    <TableRow
                      key={payment.id}
                      className="border-b border-border hover:bg-muted/50 transition-colors"
                    >
                      <TableCell className="px-6 py-4">
                        <div className="font-mono text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                          {payment.id.substring(0, 8)}
                        </div>
                      </TableCell>
                      <TableCell className="px-6 py-4">
                        <div className="min-w-0">
                          <div className="font-medium text-foreground truncate">
                            {payment.rental.user.name}
                          </div>
                          <div className="text-xs text-muted-foreground font-mono">
                            {payment.rental.user.phone}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="px-6 py-4">
                        <div className="min-w-0">
                          <div className="font-medium text-foreground truncate">
                            {payment.rental.product.name}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {payment.rental.product.capacity} KVA
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="px-6 py-4 font-medium text-foreground">
                        {formatCurrency(payment.amount)}
                      </TableCell>
                      <TableCell className="px-6 py-4 font-medium text-foreground">
                        {formatCurrency(payment.deposit)}
                      </TableCell>
                      <TableCell className="px-6 py-4 font-medium text-foreground">
                        {formatCurrency(payment.remaining)}
                      </TableCell>
                      <TableCell className="px-6 py-4">
                        {hasOvertime ? (
                          <span className="text-orange-600 dark:text-orange-400 font-medium">
                            {formatCurrency(payment.overtime || 0)}
                          </span>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell className="px-6 py-4">
                        <Badge className={statusBadge.color}>
                          {statusBadge.label}
                        </Badge>
                      </TableCell>
                      <TableCell className="px-6 py-4">
                        <PaymentActionsMenu
                          paymentId={payment.id}
                          isPending={isPending}
                          needsRemainingPayment={needsRemainingPayment}
                        />
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
