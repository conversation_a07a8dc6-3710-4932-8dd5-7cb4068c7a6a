// Test Fonnte API Token
const token = "vEnTXcGAbVfs3B5MjQ61";
const adminPhone = "6285737289529";

async function testFonnteAPI() {
  try {
    console.log("🧪 Testing Fonnte API...");
    console.log("📱 Token:", token);
    console.log("📞 Admin Phone:", adminPhone);

    const response = await fetch("https://api.fonnte.com/send", {
      method: "POST",
      headers: {
        Authorization: token,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        target: adminPhone,
        message:
          "🧪 TEST MESSAGE\n\nHalo! Ini adalah test message dari sistem rental genset.\n\nJika Anda menerima pesan ini, berarti konfigurasi WhatsApp sudah benar! ✅\n\nTerima kasih! 🙏",
        countryCode: "62",
      }),
    });

    const result = await response.json();

    console.log("\n📊 Response Status:", response.status);
    console.log("📋 Response Data:", JSON.stringify(result, null, 2));

    if (response.ok && result.status) {
      console.log("\n✅ SUCCESS! Token is valid and message sent!");
      console.log("📱 Message ID:", result.id || "unknown");
      console.log("🎉 Check your WhatsApp at +6285737289529");
    } else {
      console.log("\n❌ FAILED! Token might be invalid or expired");
      console.log(
        "🔍 Error:",
        result.reason || result.message || "Unknown error"
      );
    }
  } catch (error) {
    console.error("\n💥 Network Error:", error.message);
  }
}

testFonnteAPI();
