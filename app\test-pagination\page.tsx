"use client";

import React, { useState } from 'react';

export default function TestPaginationPage() {
  const [currentPage, setCurrentPage] = useState(1);
  const totalPages = 3;
  
  const handlePageChange = (page: number) => {
    console.log('🔥 handlePageChange called with:', page);
    setCurrentPage(page);
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Test Pagination</h1>
      
      <div className="mb-4">
        <p>Current Page: <strong>{currentPage}</strong></p>
        <p>Total Pages: <strong>{totalPages}</strong></p>
      </div>

      {/* Simple Pagination Test */}
      <div className="flex items-center gap-2 mb-8">
        <button
          onClick={() => {
            console.log('🔙 Previous clicked');
            handlePageChange(currentPage - 1);
          }}
          disabled={currentPage <= 1}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          Previous
        </button>
        
        {[1, 2, 3].map(page => (
          <button
            key={page}
            onClick={() => {
              console.log('🔢 Page clicked:', page);
              handlePageChange(page);
            }}
            className={`px-4 py-2 rounded ${
              page === currentPage 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            {page}
          </button>
        ))}
        
        <button
          onClick={() => {
            console.log('🔜 Next clicked');
            handlePageChange(currentPage + 1);
          }}
          disabled={currentPage >= totalPages}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          Next
        </button>
      </div>

      {/* Test Data */}
      <div className="grid grid-cols-1 gap-2">
        <h2 className="text-lg font-semibold mb-2">Test Data for Page {currentPage}:</h2>
        {currentPage === 1 && (
          <>
            <div className="p-4 bg-gray-100 rounded">Item 1</div>
            <div className="p-4 bg-gray-100 rounded">Item 2</div>
            <div className="p-4 bg-gray-100 rounded">Item 3</div>
          </>
        )}
        {currentPage === 2 && (
          <>
            <div className="p-4 bg-gray-100 rounded">Item 4</div>
            <div className="p-4 bg-gray-100 rounded">Item 5</div>
            <div className="p-4 bg-gray-100 rounded">Item 6</div>
          </>
        )}
        {currentPage === 3 && (
          <>
            <div className="p-4 bg-gray-100 rounded">Item 7</div>
            <div className="p-4 bg-gray-100 rounded">Item 8</div>
          </>
        )}
      </div>
    </div>
  );
}
