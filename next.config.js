/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "placehold.co",
      },
      {
        protocol: "https",
        hostname: "**",
      },
    ],
  },
  // Temporarily disable CSP for debugging
  // async headers() {
  //   return [
  //     {
  //       source: "/(.*)",
  //       headers: [
  //         {
  //           key: "Content-Security-Policy",
  //           value: [
  //             "default-src 'self'",
  //             "script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: https://app.sandbox.midtrans.com https://api.sandbox.midtrans.com https://d2n7cdqdrlte95.cloudfront.net https://d2f3dnusg0rbp7.cloudfront.net https://pay.google.com https://js-agent.newrelic.com https://bam.nr-data.net",
  //             "worker-src 'self' blob:",
  //             "child-src 'self' blob:",
  //             "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
  //             "font-src 'self' https://fonts.gstatic.com",
  //             "img-src 'self' data: blob: https: http: https://*.openstreetmap.org https://*.tile.openstreetmap.org https://tile.openstreetmap.org",
  //             "connect-src 'self' https://api.sandbox.midtrans.com https://app.sandbox.midtrans.com https://nominatim.openstreetmap.org https://photon.komoot.io https://bam.nr-data.net https://*.openstreetmap.org https://*.tile.openstreetmap.org https://tile.openstreetmap.org",
  //             "frame-src 'self' https://app.sandbox.midtrans.com https://pay.google.com",
  //             "object-src 'none'",
  //             "base-uri 'self'",
  //             "form-action 'self'",
  //             "frame-ancestors 'none'",
  //             "upgrade-insecure-requests",
  //           ].join("; "),
  //         },
  //         {
  //           key: "X-Frame-Options",
  //           value: "SAMEORIGIN",
  //         },
  //         {
  //           key: "X-Content-Type-Options",
  //           value: "nosniff",
  //         },
  //         {
  //           key: "Referrer-Policy",
  //           value: "strict-origin-when-cross-origin",
  //         },
  //       ],
  //     },
  //   ];
  // },
};

module.exports = nextConfig;
