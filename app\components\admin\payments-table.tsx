"use client";

import React, { useState, useMemo } from "react";
import { formatCurrency } from "@/lib/utils/format";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table";
import { Badge } from "@/app/components/ui/badge";
import { PaymentActionsMenu } from "@/app/components/admin/payment-actions-menu";

import { Pagination, usePagination } from "@/app/components/ui/pagination";
import { PaymentStatus } from "@prisma/client";

type PaymentWithRelations = {
  id: string;
  amount: number;
  deposit: number;
  remaining: number;
  overtime: number | null;
  status: PaymentStatus;
  transactionId: string | null;
  createdAt: Date;
  updatedAt: Date;
  rental: {
    operationalEnd: Date | null;
    user: {
      name: string;
      phone: string;
    };
    product: {
      name: string;
      capacity: number;
    };
  };
};

interface PaymentsTableProps {
  payments: PaymentWithRelations[];
  searchQuery?: string;
  statusFilter?: string;
}

// Fungsi untuk mendapatkan tampilan status pembayaran
function getPaymentStatusBadge(status: string) {
  switch (status.toUpperCase()) {
    case "FULLY_PAID":
      return { label: "Lunas", variant: "default" as const, color: "bg-green-100 text-green-800" };
    case "DEPOSIT_PAID":
      return { label: "Deposit Dibayar", variant: "success" as const, color: "bg-blue-100 text-blue-800" };
    case "DEPOSIT_PENDING":
      return { label: "Menunggu Deposit", variant: "secondary" as const, color: "bg-yellow-100 text-yellow-800" };
    case "FAILED":
      return { label: "Gagal", variant: "destructive" as const, color: "bg-red-100 text-red-800" };
    default:
      return { label: "Menunggu", variant: "outline" as const, color: "bg-gray-100 text-gray-800" };
  }
}

export function PaymentsTable({ payments, searchQuery = "", statusFilter = "" }: PaymentsTableProps) {
  // Filter payments based on search and status
  const filteredPayments = useMemo(() => {
    let filtered = payments;

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(payment =>
        payment.rental.user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        payment.rental.user.phone.toLowerCase().includes(searchQuery.toLowerCase()) ||
        payment.rental.product.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return filtered;
  }, [payments, searchQuery]);

  // Pagination
  const {
    currentPage,
    totalPages,
    startIndex,
    endIndex,
    goToPage,
    resetPage
  } = usePagination(filteredPayments.length, 5);

  // Reset page when search changes
  React.useEffect(() => {
    resetPage();
  }, [searchQuery, statusFilter, resetPage]);

  const paginatedPayments = useMemo(() => {
    return filteredPayments.slice(startIndex, endIndex);
  }, [filteredPayments, startIndex, endIndex]);

  return (
    <div className="space-y-6">
      <Card className="border-border shadow-sm">
        <CardHeader className="border-b border-border bg-muted/50">
          <CardTitle className="text-foreground">Daftar Pembayaran</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="border-b border-border bg-muted/30 hover:bg-muted/50">
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">ID</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Pelanggan</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Produk</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Total</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Deposit (50%)</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Sisa (50%)</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Overtime</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Status</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium w-[60px]">Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedPayments.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-12 text-muted-foreground">
                      {searchQuery ? `Tidak ada hasil untuk "${searchQuery}"` : "Tidak ada data pembayaran"}
                    </TableCell>
                  </TableRow>
                ) : (
                  paginatedPayments.map((payment) => {
                    const statusBadge = getPaymentStatusBadge(payment.status);
                    const hasOvertime = payment.overtime && payment.overtime > 0;
                    const isPending = payment.status === "DEPOSIT_PENDING";
                    const isDepositPaid = payment.status === "DEPOSIT_PAID";
                    const isCompleted = payment.rental.operationalEnd != null;
                    const needsRemainingPayment = isDepositPaid && isCompleted;

                    return (
                      <TableRow
                        key={payment.id}
                        className="border-b border-border hover:bg-muted/50 transition-colors"
                      >
                        <TableCell className="px-6 py-4">
                          <div className="font-mono text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                            {payment.id.substring(0, 8)}
                          </div>
                        </TableCell>
                        <TableCell className="px-6 py-4">
                          <div className="min-w-0">
                            <div className="font-medium text-foreground truncate">
                              {payment.rental.user.name}
                            </div>
                            <div className="text-xs text-muted-foreground font-mono">
                              {payment.rental.user.phone}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="px-6 py-4">
                          <div className="min-w-0">
                            <div className="font-medium text-foreground truncate">
                              {payment.rental.product.name}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {payment.rental.product.capacity} KVA
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="px-6 py-4 font-medium text-foreground">
                          {formatCurrency(payment.amount)}
                        </TableCell>
                        <TableCell className="px-6 py-4 font-medium text-foreground">
                          {formatCurrency(payment.deposit)}
                        </TableCell>
                        <TableCell className="px-6 py-4 font-medium text-foreground">
                          {formatCurrency(payment.remaining)}
                        </TableCell>
                        <TableCell className="px-6 py-4">
                          {hasOvertime ? (
                            <span className="text-orange-600 dark:text-orange-400 font-medium">
                              {formatCurrency(payment.overtime || 0)}
                            </span>
                          ) : (
                            <span className="text-muted-foreground">-</span>
                          )}
                        </TableCell>
                        <TableCell className="px-6 py-4">
                          <Badge className={statusBadge.color}>
                            {statusBadge.label}
                          </Badge>
                        </TableCell>
                        <TableCell className="px-6 py-4">
                          <PaymentActionsMenu
                            paymentId={payment.id}
                            isPending={isPending}
                            needsRemainingPayment={needsRemainingPayment}
                          />
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      {filteredPayments.length > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={filteredPayments.length}
          itemsPerPage={5}
          onPageChange={goToPage}
          className="mt-6"
        />
      )}
    </div>
  );
}
