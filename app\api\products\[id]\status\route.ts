import { auth } from "@/auth";
import { prisma } from "@/lib/config/prisma";
import { NextResponse } from "next/server";
import { ProductStatus } from "@prisma/client";

export const dynamic = 'force-dynamic';

export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user || session.user.role !== 'ADMIN') {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { id } = params;
    const { status } = await request.json();

    const updatedProduct = await prisma.product.update({
      where: { id },
      data: { status: status as ProductStatus },
    });

    return NextResponse.json(updatedProduct);
  } catch (error) {
    console.error('Error updating product status:', error);
    return new NextResponse("Error updating status", { status: 500 });
  }
} 