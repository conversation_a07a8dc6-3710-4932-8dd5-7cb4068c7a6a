# Script untuk mengganti semua import dari app/component/ menjadi app/components/

$files = Get-ChildItem -Path . -Recurse -Include "*.ts", "*.tsx", "*.js", "*.jsx" | Where-Object { $_.FullName -notlike "*node_modules*" }

$oldPattern = '@/app/component/'
$newPattern = '@/app/components/'

$totalFiles = 0
$modifiedFiles = 0

foreach ($file in $files) {
    $content = Get-Content $file.FullName -Raw -ErrorAction SilentlyContinue
    
    if ($content -and $content.Contains($oldPattern)) {
        $newContent = $content -replace [regex]::Escape($oldPattern), $newPattern
        Set-Content -Path $file.FullName -Value $newContent -NoNewline
        $modifiedFiles++
        Write-Host "Modified: $($file.FullName)"
    }
    $totalFiles++
}

Write-Host "`nSummary:"
Write-Host "Total files checked: $totalFiles"
Write-Host "Files modified: $modifiedFiles"
Write-Host "Import paths updated from '@/app/component/' to '@/app/components/'"
