import { auth } from "@/auth";
import { prisma } from "@/lib/config/prisma";
import { NextResponse } from "next/server";

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user || session.user.role !== 'ADMIN') {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { id } = params;

    // Cegah admin menghapus diri sendiri
    if (id === session.user.id) {
      return new NextResponse(
        JSON.stringify({ error: 'Tidak dapat menghapus akun diri sendiri' }),
        { status: 403, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Cek apakah target user adalah ADMIN
    const targetUser = await prisma.user.findUnique({
      where: { id },
      select: { role: true, email: true }
    });

    if (!targetUser) {
      return new NextResponse(
        JSON.stringify({ error: 'User tidak ditemukan' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Cegah admin menghapus admin lain
    if (targetUser.role === 'ADMIN') {
      return new NextResponse(
        JSON.stringify({ error: 'Tidak dapat menghapus admin lain' }),
        { status: 403, headers: { 'Content-Type': 'application/json' } }
      );
    }

    await prisma.user.delete({ where: { id } });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user || session.user.role !== 'ADMIN') {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { id } = params;
    const body = await request.json();

    const updatedUser = await prisma.user.update({
      where: { id },
      data: {
        name: body.name,
        email: body.email,
        phone: body.phone,
      },
    });

    return NextResponse.json(updatedUser);
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    );
  }
}