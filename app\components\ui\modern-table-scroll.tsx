"use client";

import React, { useRef, useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/app/components/ui/button";
import { LuChevronLeft, LuChevronRight } from "react-icons/lu";
import { cn } from "@/lib/utils/cn";

interface ModernTableScrollProps {
  children: React.ReactNode;
  className?: string;
}

export function ModernTableScroll({ children, className }: ModernTableScrollProps) {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const checkScrollability = useCallback(() => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);

      // Calculate pagination based on table columns
      const table = scrollRef.current.querySelector('table');
      if (table) {
        const headers = table.querySelectorAll('th');
        const totalColumns = headers.length;
        const columnsPerPage = 5;
        const totalPagesCalc = Math.ceil(totalColumns / columnsPerPage);

        // Calculate current page based on visible columns
        const columnWidth = scrollWidth / totalColumns;
        const visibleStartColumn = Math.floor(scrollLeft / columnWidth);
        const currentPageCalc = Math.floor(visibleStartColumn / columnsPerPage) + 1;

        setTotalPages(Math.max(totalPagesCalc, 1));
        setCurrentPage(Math.min(currentPageCalc, totalPagesCalc));
      }
    }
  }, []);

  useEffect(() => {
    const scrollElement = scrollRef.current;
    if (scrollElement) {
      checkScrollability();

      const handleScroll = () => {
        setIsScrolling(true);
        checkScrollability();

        // Clear scrolling state after animation
        setTimeout(() => setIsScrolling(false), 150);
      };

      const handleResize = () => {
        checkScrollability();
      };

      scrollElement.addEventListener('scroll', handleScroll, { passive: true });
      window.addEventListener('resize', handleResize);

      // Initial check
      const resizeObserver = new ResizeObserver(checkScrollability);
      resizeObserver.observe(scrollElement);

      return () => {
        scrollElement.removeEventListener('scroll', handleScroll);
        window.removeEventListener('resize', handleResize);
        resizeObserver.disconnect();
      };
    }
  }, [checkScrollability]);

  const scrollLeft = () => {
    if (scrollRef.current) {
      const table = scrollRef.current.querySelector('table');
      if (table) {
        const headers = table.querySelectorAll('th');
        const totalColumns = headers.length;
        const columnWidth = scrollRef.current.scrollWidth / totalColumns;
        const scrollAmount = columnWidth * 5; // 5 columns

        scrollRef.current.scrollBy({
          left: -scrollAmount,
          behavior: 'smooth'
        });
      }
    }
  };

  const scrollRight = () => {
    if (scrollRef.current) {
      const table = scrollRef.current.querySelector('table');
      if (table) {
        const headers = table.querySelectorAll('th');
        const totalColumns = headers.length;
        const columnWidth = scrollRef.current.scrollWidth / totalColumns;
        const scrollAmount = columnWidth * 5; // 5 columns

        scrollRef.current.scrollBy({
          left: scrollAmount,
          behavior: 'smooth'
        });
      }
    }
  };

  const scrollToPage = (page: number) => {
    if (scrollRef.current) {
      const table = scrollRef.current.querySelector('table');
      if (table) {
        const headers = table.querySelectorAll('th');
        const totalColumns = headers.length;
        const columnWidth = scrollRef.current.scrollWidth / totalColumns;
        const columnsPerPage = 5;
        const startColumn = (page - 1) * columnsPerPage;
        const scrollPosition = startColumn * columnWidth;

        scrollRef.current.scrollTo({
          left: scrollPosition,
          behavior: 'smooth'
        });
      }
    }
  };

  return (
    <div className="relative">
      {/* Scrollable Content */}
      <div
        ref={scrollRef}
        className={cn(
          "overflow-x-auto scrollbar-hide",
          "scroll-smooth",
          className
        )}
        style={{
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
        }}
      >
        <style jsx>{`
          div::-webkit-scrollbar {
            display: none;
          }
        `}</style>
        {children}
      </div>

      {/* Modern Pagination Controls - Only show if more than 5 columns */}
      {totalPages > 1 && (
        <div className="flex flex-col items-center gap-3 mt-4 px-4">
          {/* Column Info */}
          <div className="text-xs text-muted-foreground">
            Menampilkan kolom {((currentPage - 1) * 5) + 1}-{Math.min(currentPage * 5, scrollRef.current?.querySelector('table')?.querySelectorAll('th').length || 0)} dari {scrollRef.current?.querySelector('table')?.querySelectorAll('th').length || 0} kolom
          </div>

          {/* Pagination Controls */}
          <div className="flex items-center gap-2">
            {/* Previous Button */}
            <Button
            variant="outline"
            size="icon"
            onClick={scrollLeft}
            disabled={!canScrollLeft}
            className={cn(
              "h-10 w-10 rounded-xl border-2 transition-all duration-200",
              "bg-background/95 backdrop-blur-sm shadow-sm",
              "hover:bg-primary hover:text-primary-foreground hover:border-primary hover:scale-105",
              "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100",
              "dark:bg-gray-800/95 dark:border-gray-600 dark:hover:bg-primary"
            )}
          >
            <LuChevronLeft className="h-4 w-4" />
          </Button>

          {/* Page Numbers */}
          <div className="flex items-center gap-1">
            {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
              let pageNum;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (currentPage <= 3) {
                pageNum = i + 1;
              } else if (currentPage >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = currentPage - 2 + i;
              }

              const isActive = pageNum === currentPage;

              return (
                <Button
                  key={pageNum}
                  variant={isActive ? "default" : "outline"}
                  size="icon"
                  onClick={() => scrollToPage(pageNum)}
                  className={cn(
                    "h-10 w-10 rounded-xl border-2 transition-all duration-200 font-semibold",
                    isActive
                      ? "bg-primary text-primary-foreground border-primary shadow-lg scale-105"
                      : "bg-background/95 backdrop-blur-sm shadow-sm hover:bg-primary/10 hover:border-primary/50 hover:scale-105",
                    "dark:bg-gray-800/95 dark:border-gray-600"
                  )}
                >
                  {pageNum}
                </Button>
              );
            })}
          </div>

          {/* Next Button */}
          <Button
            variant="outline"
            size="icon"
            onClick={scrollRight}
            disabled={!canScrollRight}
            className={cn(
              "h-10 w-10 rounded-xl border-2 transition-all duration-200",
              "bg-background/95 backdrop-blur-sm shadow-sm",
              "hover:bg-primary hover:text-primary-foreground hover:border-primary hover:scale-105",
              "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100",
              "dark:bg-gray-800/95 dark:border-gray-600 dark:hover:bg-primary"
            )}
          >
            <LuChevronRight className="h-4 w-4" />
          </Button>
          </div>
        </div>
      )}
    </div>
  );
}

// Hook untuk scroll programmatically
export function useTableScroll() {
  const scrollRef = useRef<HTMLDivElement>(null);

  const scrollToColumn = (columnIndex: number) => {
    if (scrollRef.current) {
      const table = scrollRef.current.querySelector('table');
      if (table) {
        const cells = table.querySelectorAll('th, td');
        const targetCell = cells[columnIndex];
        if (targetCell) {
          targetCell.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'center'
          });
        }
      }
    }
  };

  return { scrollRef, scrollToColumn };
}
