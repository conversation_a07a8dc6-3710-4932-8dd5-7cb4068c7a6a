"use client";

import React, { useRef, useState, useEffect, useCallback } from "react";
import { Button } from "@/app/components/ui/button";
import { LuChevronLeft, LuChevronRight } from "react-icons/lu";
import { cn } from "@/lib/utils/cn";

interface ModernTableScrollProps {
  children: React.ReactNode;
  className?: string;
}

export function ModernTableScroll({ children, className }: ModernTableScrollProps) {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);

  const checkScrollability = useCallback(() => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  }, []);

  useEffect(() => {
    const scrollElement = scrollRef.current;
    if (scrollElement) {
      checkScrollability();

      const handleScroll = () => {
        setIsScrolling(true);
        checkScrollability();

        // Clear scrolling state after animation
        setTimeout(() => setIsScrolling(false), 150);
      };

      const handleResize = () => {
        checkScrollability();
      };

      scrollElement.addEventListener('scroll', handleScroll, { passive: true });
      window.addEventListener('resize', handleResize);

      // Initial check
      const resizeObserver = new ResizeObserver(checkScrollability);
      resizeObserver.observe(scrollElement);

      return () => {
        scrollElement.removeEventListener('scroll', handleScroll);
        window.removeEventListener('resize', handleResize);
        resizeObserver.disconnect();
      };
    }
  }, [checkScrollability]);

  const scrollLeft = () => {
    if (scrollRef.current) {
      const scrollAmount = scrollRef.current.clientWidth * 0.8;
      scrollRef.current.scrollBy({
        left: -scrollAmount,
        behavior: 'smooth'
      });
    }
  };

  const scrollRight = () => {
    if (scrollRef.current) {
      const scrollAmount = scrollRef.current.clientWidth * 0.8;
      scrollRef.current.scrollBy({
        left: scrollAmount,
        behavior: 'smooth'
      });
    }
  };

  return (
    <div className="relative group">
      {/* Left Scroll Button */}
      <div
        className={cn(
          "absolute left-0 top-0 bottom-0 z-10 flex items-center transition-all duration-300",
          canScrollLeft
            ? "opacity-100 translate-x-0"
            : "opacity-0 -translate-x-2 pointer-events-none"
        )}
      >
        <div className="bg-gradient-to-r from-background via-background to-transparent w-16 h-full absolute inset-0" />
        <Button
          variant="outline"
          size="icon"
          onClick={scrollLeft}
          className={cn(
            "relative z-10 ml-2 h-10 w-10 rounded-full shadow-lg border-2",
            "bg-background/95 backdrop-blur-sm",
            "hover:bg-primary hover:text-primary-foreground hover:border-primary",
            "transition-all duration-200 hover:scale-110",
            "dark:bg-gray-800/95 dark:border-gray-600 dark:hover:bg-primary",
            isScrolling && "scale-95"
          )}
        >
          <LuChevronLeft className="h-5 w-5" />
        </Button>
      </div>

      {/* Right Scroll Button */}
      <div
        className={cn(
          "absolute right-0 top-0 bottom-0 z-10 flex items-center transition-all duration-300",
          canScrollRight
            ? "opacity-100 translate-x-0"
            : "opacity-0 translate-x-2 pointer-events-none"
        )}
      >
        <div className="bg-gradient-to-l from-background via-background to-transparent w-16 h-full absolute inset-0" />
        <Button
          variant="outline"
          size="icon"
          onClick={scrollRight}
          className={cn(
            "relative z-10 mr-2 h-10 w-10 rounded-full shadow-lg border-2",
            "bg-background/95 backdrop-blur-sm",
            "hover:bg-primary hover:text-primary-foreground hover:border-primary",
            "transition-all duration-200 hover:scale-110",
            "dark:bg-gray-800/95 dark:border-gray-600 dark:hover:bg-primary",
            isScrolling && "scale-95"
          )}
        >
          <LuChevronRight className="h-5 w-5" />
        </Button>
      </div>

      {/* Scrollable Content */}
      <div
        ref={scrollRef}
        className={cn(
          "overflow-x-auto scrollbar-hide",
          "scroll-smooth",
          className
        )}
        style={{
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
        }}
      >
        <style jsx>{`
          div::-webkit-scrollbar {
            display: none;
          }
        `}</style>
        {children}
      </div>

      {/* Scroll Indicators */}
      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        {canScrollLeft && (
          <div className="w-2 h-1 bg-primary/30 rounded-full" />
        )}
        <div className="w-4 h-1 bg-primary rounded-full" />
        {canScrollRight && (
          <div className="w-2 h-1 bg-primary/30 rounded-full" />
        )}
      </div>
    </div>
  );
}

// Hook untuk scroll programmatically
export function useTableScroll() {
  const scrollRef = useRef<HTMLDivElement>(null);

  const scrollToColumn = (columnIndex: number) => {
    if (scrollRef.current) {
      const table = scrollRef.current.querySelector('table');
      if (table) {
        const cells = table.querySelectorAll('th, td');
        const targetCell = cells[columnIndex];
        if (targetCell) {
          targetCell.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'center'
          });
        }
      }
    }
  };

  return { scrollRef, scrollToColumn };
}
