// Test Fonnte API Connection untuk Auto-Send
const token = "vEnTXcGAbVfs3B5MjQ61";
const adminPhone = "6285737289529";

async function testFonnteConnection() {
  try {
    console.log("🧪 TESTING FONNTE CONNECTION FOR AUTO-SEND...");
    console.log("📱 Token:", token);
    console.log("📞 Admin Phone:", adminPhone);
    console.log("🌐 Dashboard: https://console.fonnte.com/");
    console.log("=" .repeat(60));
    
    const response = await fetch("https://api.fonnte.com/send", {
      method: "POST",
      headers: {
        Authorization: token,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        target: adminPhone,
        message: `🧪 FONNTE CONNECTION TEST

✅ Token: Valid
📱 Device: Testing Connection  
🎯 System: Rental Genset
⏰ Time: ${new Date().toLocaleString('id-ID')}

<PERSON><PERSON> p<PERSON> ini masuk, Auto-Send sudah aktif! 🎉

Sistem akan otomatis kirim notifikasi setiap ada order baru.`,
        countryCode: "62",
      }),
    });

    const result = await response.json();
    
    console.log("\n📊 RESPONSE STATUS:", response.status);
    console.log("📋 RESPONSE DATA:");
    console.log(JSON.stringify(result, null, 2));
    console.log("=" .repeat(60));
    
    if (response.ok && result.status) {
      console.log("\n🎉 SUCCESS! AUTO-SEND AKTIF!");
      console.log("📱 Message ID:", result.id || "unknown");
      console.log("✅ WhatsApp device terhubung ke Fonnte");
      console.log("🚀 Sistem rental akan auto-send notifikasi order");
      console.log("📱 Check WhatsApp di +6285737289529");
      console.log("\n🎯 NEXT STEPS:");
      console.log("1. Buat order test di sistem");
      console.log("2. Cek WhatsApp untuk notifikasi otomatis");
      console.log("3. Auto-Send sudah siap! ✅");
      
    } else {
      console.log("\n⚠️ AUTO-SEND BELUM AKTIF");
      console.log("🔍 Error:", result.reason || result.message || "Unknown error");
      
      if (result.reason === "request invalid on disconnected device") {
        console.log("\n📱 CARA MENGHUBUNGKAN WHATSAPP KE FONNTE:");
        console.log("=" .repeat(60));
        console.log("1. 🌐 Buka: https://console.fonnte.com/");
        console.log("2. 🔑 Login dengan akun Fonnte Anda");
        console.log("3. 📱 Pilih menu 'Device' atau 'WhatsApp'");
        console.log("4. ➕ Klik 'Add Device' atau 'Connect WhatsApp'");
        console.log("5. 📷 QR Code akan muncul di layar");
        console.log("6. 📱 Buka WhatsApp di HP +6285737289529");
        console.log("7. ⚙️ Masuk ke Settings → Linked Devices");
        console.log("8. 🔗 Klik 'Link a Device'");
        console.log("9. 📷 Scan QR Code dari Fonnte Dashboard");
        console.log("10. ⏳ Tunggu konfirmasi 'Device Connected'");
        console.log("11. 🧪 Test lagi: node test-fonnte-connection.js");
        console.log("=" .repeat(60));
        
      } else if (result.reason === "invalid token") {
        console.log("\n🔑 TOKEN INVALID:");
        console.log("- Update token di file .env");
        console.log("- Dapatkan token baru dari Fonnte Dashboard");
        
      } else {
        console.log("\n❓ ERROR LAIN:");
        console.log("- Cek koneksi internet");
        console.log("- Cek status Fonnte service");
        console.log("- Contact Fonnte support jika perlu");
      }
      
      console.log("\n📋 FALLBACK TERSEDIA:");
      console.log("✅ Sistem tetap berfungsi dengan manual WhatsApp URL");
      console.log("✅ Admin akan dapat link WhatsApp di console log");
      console.log("✅ Klik link untuk buka WhatsApp dengan pesan siap");
    }
    
  } catch (error) {
    console.error("\n💥 NETWORK ERROR:", error.message);
    console.log("\n🔧 TROUBLESHOOTING:");
    console.log("- Cek koneksi internet");
    console.log("- Cek firewall/proxy settings");
    console.log("- Try again dalam beberapa menit");
  }
}

console.log("🚀 Starting Fonnte Connection Test...\n");
testFonnteConnection();
