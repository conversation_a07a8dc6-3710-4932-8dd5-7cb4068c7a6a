"use client";

import React from "react";
import { Button } from "@/app/components/ui/button";
import { LuChevronLeft, LuChevronRight, LuMoreHorizontal } from "react-icons/lu";
import { cn } from "@/lib/utils/cn";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  className?: string;
}

export function Pagination({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  onPageChange,
  className
}: PaginationProps) {
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  const generatePageNumbers = () => {
    const pages: (number | string)[] = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is less than max visible
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);

      if (currentPage <= 3) {
        // Show pages 2, 3, 4 and ellipsis
        for (let i = 2; i <= 4; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      } else if (currentPage >= totalPages - 2) {
        // Show ellipsis and last 4 pages
        pages.push('...');
        for (let i = totalPages - 3; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // Show ellipsis, current page with neighbors, ellipsis
        pages.push('...');
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      }
    }

    return pages;
  };

  const pageNumbers = generatePageNumbers();

  if (totalPages <= 1) {
    return (
      <div className={cn("flex items-center justify-between px-2", className)}>
        <div className="text-sm text-muted-foreground">
          Menampilkan {totalItems} dari {totalItems} data
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col sm:flex-row items-center justify-between gap-4 px-2", className)}>
      {/* Items Info */}
      <div className="text-sm text-muted-foreground">
        Menampilkan {startItem}-{endItem} dari {totalItems} data
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center gap-2">
        {/* Previous Button */}
        <Button
          variant="outline"
          size="icon"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage <= 1}
          className={cn(
            "h-10 w-10 rounded-xl border-2 transition-all duration-200",
            "bg-background/95 backdrop-blur-sm shadow-sm",
            "hover:bg-primary hover:text-primary-foreground hover:border-primary hover:scale-105",
            "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:bg-background",
            "dark:bg-gray-800/95 dark:border-gray-600 dark:hover:bg-primary"
          )}
        >
          <LuChevronLeft className="h-4 w-4" />
        </Button>

        {/* Page Numbers */}
        <div className="flex items-center gap-1">
          {pageNumbers.map((page, index) => {
            if (page === '...') {
              return (
                <div
                  key={`ellipsis-${index}`}
                  className="flex h-10 w-10 items-center justify-center"
                >
                  <LuMoreHorizontal className="h-4 w-4 text-muted-foreground" />
                </div>
              );
            }

            const pageNum = page as number;
            const isActive = pageNum === currentPage;

            return (
              <Button
                key={pageNum}
                variant={isActive ? "default" : "outline"}
                size="icon"
                onClick={() => onPageChange(pageNum)}
                className={cn(
                  "h-10 w-10 rounded-xl border-2 transition-all duration-200 font-semibold",
                  isActive
                    ? "bg-primary text-primary-foreground border-primary shadow-lg scale-105"
                    : "bg-background/95 backdrop-blur-sm shadow-sm hover:bg-primary/10 hover:border-primary/50 hover:scale-105",
                  "dark:bg-gray-800/95 dark:border-gray-600"
                )}
              >
                {pageNum}
              </Button>
            );
          })}
        </div>

        {/* Next Button */}
        <Button
          variant="outline"
          size="icon"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage >= totalPages}
          className={cn(
            "h-10 w-10 rounded-xl border-2 transition-all duration-200",
            "bg-background/95 backdrop-blur-sm shadow-sm",
            "hover:bg-primary hover:text-primary-foreground hover:border-primary hover:scale-105",
            "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:bg-background",
            "dark:bg-gray-800/95 dark:border-gray-600 dark:hover:bg-primary"
          )}
        >
          <LuChevronRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}

// Hook untuk pagination logic
export function usePagination(totalItems: number, itemsPerPage: number = 5) {
  const [currentPage, setCurrentPage] = React.useState(1);
  
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;

  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(prev => prev + 1);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(prev => prev - 1);
    }
  };

  const resetPage = () => {
    setCurrentPage(1);
  };

  return {
    currentPage,
    totalPages,
    startIndex,
    endIndex,
    goToPage,
    goToNextPage,
    goToPreviousPage,
    resetPage,
    hasNextPage: currentPage < totalPages,
    hasPreviousPage: currentPage > 1
  };
}
