import { Metadata } from "next";
import { prisma } from "@/lib/config/prisma";
import { ProductTable } from "@/app/components/admin/product-table";
import { AdminHeader } from "@/app/components/admin/admin-header";
import { Suspense } from "react";
import { LoadingState } from "@/app/components/shared/loading-state";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { AddProductButton } from "./add-product-button";

export const metadata: Metadata = {
  title: "Manajemen Produk | Rental Ganset",
  description: "Kelola produk genset Anda",
};

export const dynamic = 'force-dynamic';
export const revalidate = 0;

async function ProductsContent() {
  try {
    const session = await auth();
    if (!session?.user || session.user.role !== "ADMIN") {
      redirect("/login");
    }

    const products = await prisma.product.findMany({
      include: {
        user: true,
      },
      orderBy: {
        createdAt: "desc"
      },
    });

    console.log('Products found in admin:', products.length);

    if (!products) {
      throw new Error("Gagal memuat data produk");
    }

    return (
      <>
        <AdminHeader 
          title="Manajemen Produk"
          description="Kelola produk genset"
          action={<AddProductButton />}
        />

        <div className="mt-5">
          <ProductTable products={products} />
        </div>
      </>
    );
  } catch (error) {
    console.error("Error loading products:", error);
    throw new Error("Gagal memuat data produk");
  }
}

export default function AdminProductsPage() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <Suspense fallback={<LoadingState />}>
        <ProductsContent />
      </Suspense>
    </div>
  );
}
