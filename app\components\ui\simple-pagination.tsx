"use client";

import React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils/cn";

interface SimplePaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  className?: string;
}

export function SimplePagination({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  onPageChange,
  className
}: SimplePaginationProps) {
  console.log('🎯 SimplePagination rendered with:', { currentPage, totalPages, totalItems });
  
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  // Don't show pagination if only 1 page
  if (totalPages <= 1) {
    return (
      <div className={cn("flex items-center justify-between px-2", className)}>
        <div className="text-sm text-muted-foreground">
          Menampilkan {totalItems} dari {totalItems} data
        </div>
      </div>
    );
  }

  const handlePageChange = (page: number) => {
    console.log('🚀 handlePageChange called with:', page);
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      console.log('✅ Calling onPageChange with:', page);
      onPageChange(page);
    } else {
      console.log('❌ Invalid page or same page:', { page, currentPage, totalPages });
    }
  };

  return (
    <div className={cn("flex flex-col sm:flex-row items-center justify-between gap-4 px-2", className)}>
      {/* Items Info */}
      <div className="text-sm text-muted-foreground">
        Menampilkan {startItem}-{endItem} dari {totalItems} data
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center gap-2">
        {/* Previous Button */}
        <button
          type="button"
          onClick={() => {
            console.log('🔙 Previous clicked, current:', currentPage);
            handlePageChange(currentPage - 1);
          }}
          disabled={currentPage <= 1}
          className="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
        >
          <ChevronLeft className="h-4 w-4" />
          Previous
        </button>

        {/* Page Numbers */}
        <div className="flex items-center gap-1">
          {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
            <button
              key={page}
              type="button"
              onClick={() => {
                console.log('🔢 Page clicked:', page);
                handlePageChange(page);
              }}
              className={cn(
                "px-3 py-2 text-sm border rounded-md min-w-[40px]",
                page === currentPage
                  ? "bg-blue-500 text-white border-blue-500"
                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
              )}
            >
              {page}
            </button>
          ))}
        </div>

        {/* Next Button */}
        <button
          type="button"
          onClick={() => {
            console.log('🔜 Next clicked, current:', currentPage);
            handlePageChange(currentPage + 1);
          }}
          disabled={currentPage >= totalPages}
          className="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
        >
          Next
          <ChevronRight className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
}

// Hook untuk pagination logic
export function useSimplePagination(totalItems: number, itemsPerPage: number = 5) {
  const [currentPage, setCurrentPage] = React.useState(1);

  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;

  const goToPage = (page: number) => {
    console.log('🔥 goToPage called:', { page, currentPage, totalPages });
    if (page >= 1 && page <= totalPages) {
      console.log('✅ Setting page to:', page);
      setCurrentPage(page);
      console.log('📄 Page should be set to:', page);
    } else {
      console.log('❌ Invalid page:', page, 'totalPages:', totalPages);
    }
  };

  const resetPage = () => {
    setCurrentPage(1);
  };

  return {
    currentPage,
    totalPages,
    startIndex,
    endIndex,
    goToPage,
    resetPage,
    hasNextPage: currentPage < totalPages,
    hasPreviousPage: currentPage > 1
  };
}
