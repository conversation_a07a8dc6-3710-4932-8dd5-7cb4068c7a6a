import { prisma } from "@/lib/config/prisma";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import Link from "next/link";
import { formatDate, formatCurrency } from "@/lib/utils/format";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table";
import { Button } from "@/app/components/ui/button";
import { Badge } from "@/app/components/ui/badge";
import { Search, MoreVertical, Eye, CheckCircle } from "lucide-react";
import { Input } from "@/app/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu";
import { ModernTableScroll } from "@/app/components/ui/modern-table-scroll";

// Pastikan halaman selalu up-to-date
export const dynamic = 'force-dynamic';

// Fungsi untuk mendapatkan nama status dalam bahasa Indonesia
function getStatusName(status: string): string {
  // Konversi status ke uppercase untuk konsistensi
  const normalizedStatus = status.toUpperCase();

  const statusMap: Record<string, string> = {
    'PENDING': 'Menunggu',
    'CONFIRMED': 'Dikonfirmasi',
    'ACTIVE': 'Operasional',
    'COMPLETED': 'Selesai',
    'CANCELLED': 'Dibatalkan',
    // Alias untuk kompatibilitas
    'OPERATIONAL': 'Operasional',
    'CANCELED': 'Dibatalkan',
  };

  return statusMap[normalizedStatus] || status;
}

// Fungsi untuk mendapatkan warna status
function getStatusColor(status: string): string {
  // Konversi status ke uppercase untuk konsistensi
  const normalizedStatus = status.toUpperCase();

  const statusColorMap: Record<string, string> = {
    'PENDING': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    'CONFIRMED': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    'ACTIVE': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    'COMPLETED': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
    'CANCELLED': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    // Alias untuk kompatibilitas
    'OPERATIONAL': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    'CANCELED': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  };

  return statusColorMap[normalizedStatus] || 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
}

export default async function AdminRentalsPage({
  searchParams
}: {
  searchParams: { q?: string }
}) {
  const session = await auth();

  // Redirect ke login jika tidak ada session atau bukan admin
  if (!session?.user || session.user.role !== "ADMIN") {
    redirect('/login');
  }

  // Ambil parameter pencarian dan tunggu resolusinya
  const params = await Promise.resolve(searchParams);
  const searchQuery = params.q || '';

  // Ambil data penyewaan dari database
  const rentals = await prisma.rental.findMany({
    where: {
      OR: [
        { product: { name: { contains: searchQuery, mode: 'insensitive' } } },
        { address: { contains: searchQuery, mode: 'insensitive' } },
        { notes: { contains: searchQuery, mode: 'insensitive' } }
      ]
    },
    include: {
      product: true,
      user: {
        select: {
          name: true,
          email: true,
          phone: true
        }
      },
      payment: true
    },
    orderBy: {
      startDate: 'desc'
    }
  });

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Daftar Penyewaan</h1>
        <div className="flex items-center gap-2">
          <form className="relative" action="/admin/rentals">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              name="q"
              placeholder="Cari penyewaan..."
              className="pl-9 w-[250px]"
              defaultValue={searchQuery}
            />
          </form>
          <Link href="/admin/operations">
            <Button variant="outline" className="dark:border-gray-700 dark:text-gray-200 dark:hover:bg-gray-800">Lihat Operasi</Button>
          </Link>
        </div>
      </div>

      <Card className="border dark:border-gray-800">
        <CardHeader>
          <CardTitle>Semua Penyewaan</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <ModernTableScroll>
            <Table>
            <TableHeader className="bg-blue-50 dark:bg-gray-800">
              <TableRow>
                <TableHead>No</TableHead>
                <TableHead>Produk</TableHead>
                <TableHead>Pelanggan</TableHead>
                <TableHead>Tanggal Mulai</TableHead>
                <TableHead>Waktu Kedatangan</TableHead>
                <TableHead>Durasi</TableHead>
                <TableHead>Alamat</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Total</TableHead>
                <TableHead className="w-[60px]">Aksi</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className="dark:bg-gray-900">
              {rentals.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={10} className="text-center py-6">
                    Tidak ada data penyewaan
                  </TableCell>
                </TableRow>
              ) : (
                rentals.map((rental, index) => (
                  <TableRow key={rental.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <TableCell>{index + 1}</TableCell>
                    <TableCell>
                      <div className="font-medium dark:text-white">{rental.product.name}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{rental.product.capacity} KVA</div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium dark:text-white">{rental.user.name}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{rental.user.phone}</div>
                    </TableCell>
                    <TableCell className="dark:text-gray-300">{formatDate(rental.startDate)}</TableCell>
                    <TableCell className="dark:text-gray-300">{rental.arrivalTime || '-'}</TableCell>
                    <TableCell className="dark:text-gray-300">
                      {rental.duration ? rental.duration.replace('_HOURS', '').replace('x8', ' x 8 Jam') : '-'}
                    </TableCell>
                    <TableCell>
                      <div className="max-w-[200px] truncate dark:text-gray-300" title={rental.address || '-'}>
                        {rental.address || '-'}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(rental.status)}>
                        {getStatusName(rental.status)}
                      </Badge>
                    </TableCell>
                    <TableCell className="dark:text-gray-300">{formatCurrency(rental.amount)}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="w-8 h-8 p-0">
                            <MoreVertical className="h-4 w-4 dark:text-gray-300" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="dark:bg-gray-800 dark:border-gray-700">
                          <DropdownMenuItem asChild className="dark:text-gray-300 dark:hover:bg-gray-700">
                            <Link href={`/admin/rentals/${rental.id}`}>
                              <Eye className="mr-2 h-4 w-4" />
                              Lihat Detail
                            </Link>
                          </DropdownMenuItem>
                          {rental.status.toUpperCase() === 'PENDING' && (
                            <DropdownMenuItem asChild className="dark:text-gray-300 dark:hover:bg-gray-700">
                              <Link href={`/admin/rentals/${rental.id}/confirm`}>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Konfirmasi
                              </Link>
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
            </Table>
          </ModernTableScroll>
        </CardContent>
      </Card>
    </div>
  );
}
