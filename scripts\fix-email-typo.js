const { PrismaClient } = require("@prisma/client");

const prisma = new PrismaClient();

async function fixEmailTypo() {
  try {
    console.log("🔧 Fixing email typo...\n");

    // Get users with typo
    const usersWithTypo = await prisma.user.findMany({
      where: {
        email: {
          endsWith: "@gamil.com",
        },
      },
    });

    console.log(`Found ${usersWithTypo.length} users with email typo:`);

    for (const user of usersWithTypo) {
      const correctedEmail = user.email.replace("@gamil.com", "@gmail.com");

      // Check if corrected email already exists
      const existingUser = await prisma.user.findFirst({
        where: { email: correctedEmail },
      });

      if (existingUser) {
        console.log(
          `⚠️  Cannot fix ${user.email} -> ${correctedEmail} (already exists)`
        );
        continue;
      }

      // Update the email
      await prisma.user.update({
        where: { id: user.id },
        data: { email: correctedEmail },
      });

      console.log(`✅ Fixed: ${user.email} -> ${correctedEmail}`);
    }

    console.log("\n🎉 Email typo fix completed!");
  } catch (error) {
    console.error("❌ Error fixing email typo:", error);
  } finally {
    await prisma.$disconnect();
  }
}

fixEmailTypo();
