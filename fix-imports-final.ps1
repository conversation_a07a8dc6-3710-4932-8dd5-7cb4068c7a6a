# Script untuk mengganti semua import dari app/component/ menjadi app/components/
# Hanya memproses file yang ada dan mengandung pattern yang dicari

Write-Host "Starting import path fix..."

$files = Get-ChildItem -Path "app" -Recurse -Include "*.ts", "*.tsx" | Where-Object { Test-Path $_.FullName }
$oldPattern = '@/app/component/'
$newPattern = '@/app/components/'

$totalFiles = 0
$modifiedFiles = 0

foreach ($file in $files) {
    try {
        $content = Get-Content $file.FullName -Raw -ErrorAction Stop
        
        if ($content -and $content.Contains($oldPattern)) {
            $newContent = $content -replace [regex]::Escape($oldPattern), $newPattern
            Set-Content -Path $file.FullName -Value $newContent -NoNewline -ErrorAction Stop
            $modifiedFiles++
            Write-Host "Fixed: $($file.FullName)" -ForegroundColor Green
        }
        $totalFiles++
    }
    catch {
        Write-Host "Error processing $($file.FullName): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nSummary:" -ForegroundColor Yellow
Write-Host "Total files checked: $totalFiles"
Write-Host "Files modified: $modifiedFiles"
Write-Host "Import paths updated from '@/app/component/' to '@/app/components/'" -ForegroundColor Green

# Juga perbaiki file di lib yang mungkin menggunakan import lama
Write-Host "`nChecking lib folder..."
$libFiles = Get-ChildItem -Path "lib" -Recurse -Include "*.ts", "*.tsx" | Where-Object { Test-Path $_.FullName }
$libModified = 0

foreach ($file in $libFiles) {
    try {
        $content = Get-Content $file.FullName -Raw -ErrorAction Stop
        
        if ($content -and $content.Contains($oldPattern)) {
            $newContent = $content -replace [regex]::Escape($oldPattern), $newPattern
            Set-Content -Path $file.FullName -Value $newContent -NoNewline -ErrorAction Stop
            $libModified++
            Write-Host "Fixed lib file: $($file.FullName)" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "Error processing lib file $($file.FullName): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "Lib files modified: $libModified"
Write-Host "`nImport fix completed!" -ForegroundColor Green
