// Test Deposit Payment Notification
const orderId = "cmb8xch9z0001tmoklrglfzsb"; // Order ID yang baru dibuat

async function testDepositPayment() {
  try {
    console.log("🧪 Testing Deposit Payment Notification...");
    console.log("📋 Order ID:", orderId);
    console.log("💰 Simulating deposit payment webhook...");
    
    // Simulate Midtrans webhook for deposit payment
    const webhookData = {
      order_id: `${orderId}_deposit_${Date.now()}`,
      transaction_status: "settlement",
      fraud_status: "accept",
      transaction_id: `TXN_${Date.now()}`,
      gross_amount: "500000.00", // 500k deposit
      payment_type: "bank_transfer"
    };

    console.log("📤 Sending webhook data:", JSON.stringify(webhookData, null, 2));

    const response = await fetch('http://localhost:3000/api/payments/webhook', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(webhookData)
    });

    const result = await response.json();
    
    console.log("\n📊 Webhook Response Status:", response.status);
    console.log("📋 Webhook Response:", JSON.stringify(result, null, 2));
    
    if (response.ok && result.success) {
      console.log("\n🎉 SUCCESS! Deposit payment webhook processed!");
      console.log("💰 Deposit payment should trigger WhatsApp notification");
      console.log("📱 Check WhatsApp at +************* for deposit notification");
      console.log("🔍 Check server console for WhatsApp logs");
    } else {
      console.log("\n❌ FAILED! Webhook processing failed");
      console.log("🔍 Error:", result.error || 'Unknown error');
    }
    
  } catch (error) {
    console.error("\n💥 Network Error:", error.message);
  }
}

console.log("🚀 Starting Deposit Payment Test...\n");
testDepositPayment();
