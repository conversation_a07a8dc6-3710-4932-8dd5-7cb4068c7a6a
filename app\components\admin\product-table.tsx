"use client";

import { formatCurrency } from "@/lib/utils/format";
import React, { useState, useMemo } from "react";
import { EditProductModal } from "../product/edit-product-modal";
import { MoreVertical, Search } from "lucide-react";
import { Button } from "../ui/button";
import { Input } from "../ui/input";

import { useDebounce } from "@/lib/hooks";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { ProductStatus } from "@prisma/client";
import { User } from "@/lib/types/user";
import Image from "next/image";
import { toast } from "sonner";

interface TableProduct {
  id: string;
  name: string;
  description: string | null;
  price: number;
  imageUrl: string | null;
  image: string | null;
  capacity: number;
  stock: number;
  status: ProductStatus;
  category: string | null;
  overtimeRate: number | null;
  userId: string;
  user?: User;
  createdAt: Date;
  updatedAt: Date;
}

interface ProductTableProps {
  products: TableProduct[];
}

export function ProductTable({ products }: ProductTableProps) {
  const [selectedProduct, setSelectedProduct] = useState<TableProduct | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [search, setSearch] = useState("");
  const debouncedSearch = useDebounce(search, 300);

  // Filter products based on search
  const filteredProducts = useMemo(() => {
    if (!debouncedSearch) return products;

    return products.filter(product =>
      product.name.toLowerCase().includes(debouncedSearch.toLowerCase()) ||
      product.description?.toLowerCase().includes(debouncedSearch.toLowerCase()) ||
      product.category?.toLowerCase().includes(debouncedSearch.toLowerCase())
    );
  }, [products, debouncedSearch]);

  // Show all filtered products (no pagination for now to avoid hydration issues)
  const paginatedProducts = filteredProducts;

  const handleEdit = (product: TableProduct) => {
    setSelectedProduct(product);
    setIsEditModalOpen(true);
  };

  const handleUpdate = () => {
    // Refresh data
    window.location.reload();
  };

  const handleStatusChange = async (product: TableProduct) => {
    try {
      const newStatus = product.status === 'AVAILABLE' ? 'NOT_AVAILABLE' : 'AVAILABLE';
      const response = await fetch(`/api/products/${product.id}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        throw new Error('Gagal mengubah status');
      }

      toast.success('Status berhasil diubah');
      handleUpdate();
    } catch (error) {
      console.error('Error updating status:', error);
      toast.error('Gagal mengubah status');
    }
  };

  const handleDelete = async (product: TableProduct) => {
    if (!confirm('Apakah Anda yakin ingin menghapus produk ini?')) {
      return;
    }

    try {
      const response = await fetch(`/api/products/${product.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Gagal menghapus produk');
      }

      toast.success('Produk berhasil dihapus');
      handleUpdate();
    } catch (error) {
      console.error('Error deleting product:', error);
      toast.error('Gagal menghapus produk');
    }
  };

  return (
    <div className="space-y-6">
      {/* Search Bar */}
      <div className="flex items-center justify-between">
        <div className="relative w-full max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            type="text"
            placeholder="Cari produk..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-10"
          />
        </div>
        {debouncedSearch && (
          <div className="text-sm text-muted-foreground">
            Menampilkan {filteredProducts.length} hasil untuk &quot;{debouncedSearch}&quot;
          </div>
        )}
      </div>

      <div className="overflow-x-auto scrollbar-hide bg-card rounded-lg border border-border shadow-sm">
        <table className="min-w-full divide-y divide-border">
          <thead className="bg-blue-50 dark:bg-gray-800">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Produk
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Harga
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Tarif Overtime
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Stok
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-20">
                Aksi
              </th>
            </tr>
          </thead>
          <tbody className="bg-card divide-y divide-border">
            {paginatedProducts.length === 0 ? (
              <tr>
                <td colSpan={6} className="px-6 py-12 text-center">
                  <div className="flex flex-col items-center justify-center space-y-3">
                    <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                      <Search className="w-6 h-6 text-muted-foreground" />
                    </div>
                    <div className="text-sm font-medium text-foreground">
                      Tidak ada produk ditemukan
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {debouncedSearch
                        ? `Tidak ada hasil untuk "${debouncedSearch}"`
                        : 'Belum ada produk yang ditambahkan'
                      }
                    </div>
                  </div>
                </td>
              </tr>
            ) : (
              paginatedProducts.map((product) => (
              <tr key={product.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    {product.imageUrl && (
                      <div className="relative h-10 w-10">
                        <Image
                          src={product.imageUrl}
                          alt={product.name}
                          fill
                          sizes="(max-width: 768px) 40px"
                          className="rounded-lg object-cover"
                        />
                      </div>
                    )}
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {product.name}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {product.capacity} KVA
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-100">
                    {formatCurrency(product.price)}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-100">
                    {formatCurrency(product.overtimeRate || 0)}
                    <div className="text-xs text-gray-500 dark:text-gray-400">per jam</div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-100">{product.stock}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    product.status === 'AVAILABLE'
                      ? 'bg-blue-100 text-blue-800 dark:bg-blue-700 dark:text-blue-100'
                      : 'bg-red-100 text-red-800 dark:bg-red-700 dark:text-red-100'
                  }`}>
                    {product.status === 'AVAILABLE' ? 'Tersedia' : 'Tidak Tersedia'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0 text-gray-500 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
                        <MoreVertical className="h-4 w-4 dark:text-gray-300" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="dark:bg-gray-800 dark:border-gray-700">
                      <DropdownMenuItem onClick={() => handleEdit(product)} className="dark:text-gray-200 dark:hover:bg-gray-700">
                        Edit Detail
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleStatusChange(product)} className="dark:text-gray-200 dark:hover:bg-gray-700">
                        {product.status === 'AVAILABLE' ? 'Set Tidak Tersedia' : 'Set Tersedia'}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator className="dark:border-gray-700" />
                      <DropdownMenuItem
                        onClick={() => handleDelete(product)}
                        className="text-red-600 focus:text-red-600 focus:bg-red-50 dark:text-red-400 dark:focus:text-red-400 dark:focus:bg-red-950 dark:hover:bg-gray-700"
                      >
                        Hapus
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </td>
              </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination temporarily disabled to avoid hydration issues */}

      {selectedProduct && selectedProduct.user && (
        <EditProductModal
          product={selectedProduct}
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onUpdate={handleUpdate}
        />
      )}
    </div>
  );
}
