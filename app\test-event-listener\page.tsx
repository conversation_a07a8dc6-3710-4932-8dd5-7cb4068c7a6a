"use client";

import React, { useState, useEffect, useRef } from 'react';

export default function TestEventListenerPage() {
  const [currentPage, setCurrentPage] = useState(1);
  const [clickCount, setClickCount] = useState(0);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const page1Ref = useRef<HTMLButtonElement>(null);
  const page2Ref = useRef<HTMLButtonElement>(null);
  const page3Ref = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    const button = buttonRef.current;
    const page1 = page1Ref.current;
    const page2 = page2Ref.current;
    const page3 = page3Ref.current;

    const handleClick = () => {
      console.log('Button clicked via addEventListener!');
      setClickCount(prev => prev + 1);
    };

    const handlePage1 = () => {
      console.log('Page 1 clicked via addEventListener!');
      setCurrentPage(1);
    };

    const handlePage2 = () => {
      console.log('Page 2 clicked via addEventListener!');
      setCurrentPage(2);
    };

    const handlePage3 = () => {
      console.log('Page 3 clicked via addEventListener!');
      setCurrentPage(3);
    };

    if (button) {
      button.addEventListener('click', handleClick);
    }
    if (page1) {
      page1.addEventListener('click', handlePage1);
    }
    if (page2) {
      page2.addEventListener('click', handlePage2);
    }
    if (page3) {
      page3.addEventListener('click', handlePage3);
    }

    return () => {
      if (button) {
        button.removeEventListener('click', handleClick);
      }
      if (page1) {
        page1.removeEventListener('click', handlePage1);
      }
      if (page2) {
        page2.removeEventListener('click', handlePage2);
      }
      if (page3) {
        page3.removeEventListener('click', handlePage3);
      }
    };
  }, []);

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>Test Event Listener</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <p>Current Page: <strong>{currentPage}</strong></p>
        <p>Click Count: <strong>{clickCount}</strong></p>
      </div>

      {/* Test Button */}
      <div style={{ marginBottom: '20px' }}>
        <button
          ref={buttonRef}
          style={{
            padding: '10px 20px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Click Me (Count: {clickCount})
        </button>
      </div>

      {/* Pagination */}
      <div style={{ display: 'flex', gap: '10px' }}>
        <button
          ref={page1Ref}
          style={{
            padding: '8px 16px',
            backgroundColor: currentPage === 1 ? '#007bff' : '#f8f9fa',
            color: currentPage === 1 ? 'white' : '#333',
            border: '1px solid #dee2e6',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Page 1
        </button>

        <button
          ref={page2Ref}
          style={{
            padding: '8px 16px',
            backgroundColor: currentPage === 2 ? '#007bff' : '#f8f9fa',
            color: currentPage === 2 ? 'white' : '#333',
            border: '1px solid #dee2e6',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Page 2
        </button>

        <button
          ref={page3Ref}
          style={{
            padding: '8px 16px',
            backgroundColor: currentPage === 3 ? '#007bff' : '#f8f9fa',
            color: currentPage === 3 ? 'white' : '#333',
            border: '1px solid #dee2e6',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Page 3
        </button>
      </div>

      {/* Alternative: Direct state manipulation */}
      <div style={{ marginTop: '40px' }}>
        <h2>Direct State Manipulation:</h2>
        <div style={{ display: 'flex', gap: '10px' }}>
          <button
            style={{
              padding: '8px 16px',
              backgroundColor: '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
            onMouseDown={() => {
              console.log('Direct state - Page 1');
              setCurrentPage(1);
            }}
          >
            Direct Page 1
          </button>
          
          <button
            style={{
              padding: '8px 16px',
              backgroundColor: '#dc3545',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
            onMouseDown={() => {
              console.log('Direct state - Page 2');
              setCurrentPage(2);
            }}
          >
            Direct Page 2
          </button>
        </div>
      </div>

      {/* Test with setTimeout */}
      <div style={{ marginTop: '20px' }}>
        <button
          style={{
            padding: '8px 16px',
            backgroundColor: '#6f42c1',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
          onMouseDown={() => {
            console.log('Timeout test');
            setTimeout(() => {
              console.log('Setting page to 3 via timeout');
              setCurrentPage(3);
            }, 100);
          }}
        >
          Page 3 (Timeout)
        </button>
      </div>
    </div>
  );
}
