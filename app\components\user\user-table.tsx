'use client';

import React, { useState, useMemo, useCallback } from "react";
import { User } from "@/lib/types/user";
import { Search } from "lucide-react";
import { useDebounce } from "@/lib/hooks";
import { useRouter, useSearchParams } from "next/navigation";
import { UserTableRow } from "./user-table-row";

import { SimplePagination, useSimplePagination } from "@/app/components/ui/simple-pagination";

export enum Role {
  ADMIN = "admin",
  USER = "user"
}

interface UserTableProps {
  searchQuery: string;
  users: User[];
}

export function UserTable({ searchQuery, users: initialUsers = [] }: UserTableProps) {
  const [search, setSearch] = useState(searchQuery);
  const debouncedSearch = useDebounce(search, 300);
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const router = useRouter();

  const filteredUsers = useMemo(() => {
    return initialUsers.filter(user => {
      const matchesSearch = !debouncedSearch ||
        user.name?.toLowerCase().includes(debouncedSearch.toLowerCase()) ||
        user.email.toLowerCase().includes(debouncedSearch.toLowerCase());

      const matchesRole = roleFilter === 'all' || user.role === roleFilter;

      return matchesSearch && matchesRole;
    });
  }, [initialUsers, debouncedSearch, roleFilter]);

  // Pagination - Read from URL
  const [currentPage, setCurrentPage] = React.useState(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      return parseInt(urlParams.get('page') || '1', 10);
    }
    return 1;
  });
  const itemsPerPage = 5;
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;

  const goToPage = (page: number) => {
    console.log('🔥 goToPage called with:', page);
    if (page >= 1 && page <= totalPages) {
      console.log('✅ Setting page to:', page);
      setCurrentPage(page);
    }
  };

  const resetPage = () => {
    setCurrentPage(1);
  };

  // Reset page when search changes
  React.useEffect(() => {
    resetPage();
  }, [debouncedSearch, roleFilter, resetPage]);

  const paginatedUsers = useMemo(() => {
    return filteredUsers.slice(startIndex, endIndex);
  }, [filteredUsers, startIndex, endIndex]);

  const handleUpdate = useCallback(() => {
    router.refresh();
  }, [router]);

  if (initialUsers.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">Tidak ada data pengguna</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Manajemen Pengguna
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Kelola pengguna dan peran mereka dalam sistem
          </p>
        </div>

        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-3 sm:items-center">
          <select
            value={roleFilter}
            onChange={(e) => setRoleFilter(e.target.value)}
            className="min-w-[140px] h-11 rounded-lg border border-border px-3 py-2 text-sm bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-colors"
          >
            <option value="all">Semua Role</option>
            <option value="admin">Admin</option>
            <option value="user">User</option>
          </select>

          <div className="relative min-w-[280px]">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <input
              type="text"
              placeholder="Cari nama atau email pengguna..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="w-full h-11 pl-10 pr-4 py-2 text-sm border border-border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-colors"
            />
          </div>
        </div>
      </div>

      {/* Results Summary */}
      {debouncedSearch && (
        <div className="text-sm text-muted-foreground">
          Menampilkan {filteredUsers.length} hasil untuk &quot;{debouncedSearch}&quot;
        </div>
      )}

      {/* Table Container */}
      <div className="bg-card shadow-sm rounded-xl border border-border overflow-hidden">
        <div className="overflow-x-auto scrollbar-hide">
          <table className="min-w-full divide-y divide-border">
            <thead className="bg-muted/50">
              <tr>
                <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                  Pengguna
                </th>
                <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                  Kontak
                </th>
                <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                  Role
                </th>
                <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                  Terdaftar
                </th>
                <th scope="col" className="px-6 py-4 text-right text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                  Aksi
                </th>
              </tr>
            </thead>
            <tbody className="bg-card divide-y divide-border">
              {paginatedUsers.length > 0 ? (
                paginatedUsers.map((user) => (
                  <UserTableRow
                    key={user.id}
                    user={user}
                    onUpdate={handleUpdate}
                  />
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="px-6 py-12 text-center">
                    <div className="flex flex-col items-center justify-center space-y-3">
                      <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                        <Search className="w-6 h-6 text-muted-foreground" />
                      </div>
                      <div className="text-sm font-medium text-foreground">
                        Tidak ada pengguna ditemukan
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {debouncedSearch
                          ? `Tidak ada hasil untuk "${debouncedSearch}"`
                          : 'Belum ada pengguna yang terdaftar'
                        }
                      </div>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination - Inline */}
      {filteredUsers.length > 0 && totalPages > 1 && (
        <div className="flex items-center justify-between mt-6 px-2">
          <div className="text-sm text-muted-foreground">
            Menampilkan {startIndex + 1}-{Math.min(endIndex, filteredUsers.length)} dari {filteredUsers.length} data
          </div>

          <div className="flex items-center gap-2">
            <button
              onMouseDown={(e) => {
                e.preventDefault();
                console.log('🔙 Previous mousedown, current:', currentPage);
                if (currentPage > 1) {
                  setCurrentPage(currentPage - 1);
                }
              }}
              onTouchStart={(e) => {
                e.preventDefault();
                console.log('🔙 Previous touchstart, current:', currentPage);
                if (currentPage > 1) {
                  setCurrentPage(currentPage - 1);
                }
              }}
              disabled={currentPage <= 1}
              className="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>

            {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
              <form key={page} method="GET" style={{ display: 'inline' }}>
                <input type="hidden" name="page" value={page} />
                <button
                  type="submit"
                  className={`px-3 py-2 text-sm border rounded-md min-w-[40px] ${
                    page === currentPage
                      ? 'bg-blue-500 text-white border-blue-500'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {page}
                </button>
              </form>
            ))}

            <button
              onClick={() => {
                console.log('🔜 Next clicked, current:', currentPage);
                if (currentPage < totalPages) {
                  setCurrentPage(currentPage + 1);
                }
              }}
              disabled={currentPage >= totalPages}
              className="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      )}

      {/* Table Footer with Stats */}
      {filteredUsers.length > 0 && (
        <div className="flex items-center justify-between text-sm text-muted-foreground px-2 mt-4">
          <div>
            Total {filteredUsers.length} pengguna
          </div>
          <div className="flex items-center space-x-4">
            <span>Admin: {filteredUsers.filter(u => u.role === 'ADMIN' || u.role === 'admin').length}</span>
            <span>User: {filteredUsers.filter(u => u.role === 'USER' || u.role === 'user').length}</span>
          </div>
        </div>
      )}
    </div>
  );
}
