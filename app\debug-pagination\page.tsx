"use client";

import React, { useState } from 'react';

export default function DebugPaginationPage() {
  const [currentPage, setCurrentPage] = useState(1);
  const [clickCount, setClickCount] = useState(0);
  
  const handleClick = () => {
    console.log('Button clicked!');
    alert('Button clicked!');
    setClickCount(prev => prev + 1);
  };

  const handlePageChange = (page: number) => {
    console.log('Page change to:', page);
    alert(`Page change to: ${page}`);
    setCurrentPage(page);
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <h1 className="text-2xl font-bold">Debug Pagination</h1>
      
      <div className="space-y-4">
        <p>Current Page: <strong>{currentPage}</strong></p>
        <p>Click Count: <strong>{clickCount}</strong></p>
      </div>

      {/* Test 1: Basic Button */}
      <div className="space-y-2">
        <h2 className="text-lg font-semibold">Test 1: Basic Button</h2>
        <button 
          onClick={handleClick}
          className="px-4 py-2 bg-red-500 text-white rounded"
        >
          Click Me (Count: {clickCount})
        </button>
      </div>

      {/* Test 2: Inline onClick */}
      <div className="space-y-2">
        <h2 className="text-lg font-semibold">Test 2: Inline onClick</h2>
        <button 
          onClick={() => {
            console.log('Inline click!');
            alert('Inline click!');
            setCurrentPage(2);
          }}
          className="px-4 py-2 bg-blue-500 text-white rounded"
        >
          Set Page to 2
        </button>
      </div>

      {/* Test 3: Simple Pagination */}
      <div className="space-y-2">
        <h2 className="text-lg font-semibold">Test 3: Simple Pagination</h2>
        <div className="flex gap-2">
          <button
            onClick={() => handlePageChange(1)}
            className={`px-3 py-2 border rounded ${currentPage === 1 ? 'bg-blue-500 text-white' : 'bg-white'}`}
          >
            1
          </button>
          <button
            onClick={() => handlePageChange(2)}
            className={`px-3 py-2 border rounded ${currentPage === 2 ? 'bg-blue-500 text-white' : 'bg-white'}`}
          >
            2
          </button>
          <button
            onClick={() => handlePageChange(3)}
            className={`px-3 py-2 border rounded ${currentPage === 3 ? 'bg-blue-500 text-white' : 'bg-white'}`}
          >
            3
          </button>
        </div>
      </div>

      {/* Test 4: Event Object */}
      <div className="space-y-2">
        <h2 className="text-lg font-semibold">Test 4: Event Object</h2>
        <button
          onClick={(e) => {
            console.log('Event:', e);
            console.log('Target:', e.target);
            console.log('CurrentTarget:', e.currentTarget);
            alert('Check console for event details');
          }}
          className="px-4 py-2 bg-green-500 text-white rounded"
        >
          Check Event
        </button>
      </div>

      {/* Test 5: Form Button */}
      <div className="space-y-2">
        <h2 className="text-lg font-semibold">Test 5: Form Button</h2>
        <form onSubmit={(e) => e.preventDefault()}>
          <button
            type="button"
            onClick={() => {
              console.log('Form button clicked!');
              alert('Form button clicked!');
            }}
            className="px-4 py-2 bg-purple-500 text-white rounded"
          >
            Form Button
          </button>
        </form>
      </div>

      {/* Test 6: Div with onClick */}
      <div className="space-y-2">
        <h2 className="text-lg font-semibold">Test 6: Div with onClick</h2>
        <div
          onClick={() => {
            console.log('Div clicked!');
            alert('Div clicked!');
          }}
          className="px-4 py-2 bg-yellow-500 text-white rounded cursor-pointer inline-block"
        >
          Clickable Div
        </div>
      </div>
    </div>
  );
}
