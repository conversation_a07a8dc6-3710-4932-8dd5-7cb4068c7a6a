"use client";

import { useState } from "react";
import { But<PERSON> } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";

export default function TestPaymentPage() {
  const [rentalId, setRentalId] = useState("cmb8xch9z0001tmoklrglfzsb");
  const [status, setStatus] = useState("DEPOSIT_PAID");
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const updatePaymentStatus = async () => {
    setLoading(true);
    try {
      const response = await fetch("/api/payments/test-webhook", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ rentalId, status }),
      });

      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ error: "Failed to update payment status" });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>Test Payment Status Update</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Rental ID</label>
            <Input
              value={rentalId}
              onChange={(e) => setRentalId(e.target.value)}
              placeholder="Enter rental ID"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Status</label>
            <select
              value={status}
              onChange={(e) => setStatus(e.target.value)}
              className="w-full p-2 border rounded-md"
            >
              <option value="DEPOSIT_PENDING">DEPOSIT_PENDING</option>
              <option value="DEPOSIT_PAID">DEPOSIT_PAID</option>
              <option value="FULLY_PAID">FULLY_PAID</option>
              <option value="FAILED">FAILED</option>
            </select>
          </div>

          <Button
            onClick={updatePaymentStatus}
            disabled={loading}
            className="w-full"
          >
            {loading ? "Updating..." : "Update Payment Status"}
          </Button>

          {result && (
            <div className="mt-4 p-4 bg-gray-100 rounded-md">
              <pre className="text-sm overflow-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
