const { PrismaClient } = require('@prisma/client');
const { hash } = require('bcrypt-ts');

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    console.log('👤 Creating test user...\n');
    
    const email = '<EMAIL>';
    const password = 'password123';
    const hashedPassword = await hash(password, 12);

    // Check if user already exists
    const existingUser = await prisma.user.findFirst({
      where: { email }
    });

    if (existingUser) {
      console.log('⚠️  Test user already exists, updating password...');
      
      await prisma.user.update({
        where: { id: existingUser.id },
        data: { password: hashedPassword }
      });
      
      console.log('✅ Test user password updated!');
    } else {
      const user = await prisma.user.create({
        data: {
          name: 'Test User',
          email: email,
          password: hashedPassword,
          role: 'USER',
          phone: '08123456789'
        }
      });

      console.log('✅ Test user created!');
      console.log(`   ID: ${user.id}`);
      console.log(`   Name: ${user.name}`);
      console.log(`   Email: ${user.email}`);
      console.log(`   Role: ${user.role}`);
    }

    console.log('\n📋 Login credentials:');
    console.log(`   Email: ${email}`);
    console.log(`   Password: ${password}`);
    console.log('\n🌐 You can now test login in any browser!');

  } catch (error) {
    console.error('❌ Error creating test user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser();
