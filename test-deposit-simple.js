// Test Deposit Payment Notification via Status Update
const orderId = "cmb8xch9z0001tmoklrglfzsb"; // Order ID yang baru dibuat

async function testDepositPaymentSimple() {
  try {
    console.log("🧪 Testing Deposit Payment Notification (Simple)...");
    console.log("📋 Order ID:", orderId);
    
    // First, get payment ID for this rental
    console.log("🔍 Getting payment info for rental...");
    
    const paymentResponse = await fetch(`http://localhost:3000/api/rentals/${orderId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!paymentResponse.ok) {
      throw new Error(`Failed to get rental info: ${paymentResponse.status}`);
    }

    const rentalData = await paymentResponse.json();
    console.log("📋 Rental data received");
    
    // Simulate payment status update to DEPOSIT_PAID
    console.log("💰 Updating payment status to DEPOSIT_PAID...");
    
    // We'll use a test endpoint to simulate this
    const testData = {
      rentalId: orderId,
      status: "DEPOSIT_PAID"
    };

    console.log("📤 Sending test data:", JSON.stringify(testData, null, 2));

    const response = await fetch('http://localhost:3000/api/payments/test-webhook', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    });

    const result = await response.json();
    
    console.log("\n📊 Test Response Status:", response.status);
    console.log("📋 Test Response:", JSON.stringify(result, null, 2));
    
    if (response.ok && result.success) {
      console.log("\n🎉 SUCCESS! Deposit payment status updated!");
      console.log("💰 This should trigger WhatsApp notification to admin");
      console.log("📱 Check WhatsApp at +6285737289529 for deposit notification");
      console.log("🔍 Check server console for WhatsApp logs");
      console.log("\n📋 Expected WhatsApp Message:");
      console.log("💰 DEPOSIT PAYMENT RECEIVED! 💰");
      console.log("📋 Payment Details: Order ID, Amount, etc.");
      console.log("👤 Customer Information");
      console.log("🔧 Product Details");
      console.log("⚡ Next Actions Required");
    } else {
      console.log("\n❌ FAILED! Status update failed");
      console.log("🔍 Error:", result.error || 'Unknown error');
    }
    
  } catch (error) {
    console.error("\n💥 Error:", error.message);
  }
}

console.log("🚀 Starting Simple Deposit Payment Test...\n");
testDepositPaymentSimple();
