# Script untuk membersihkan cache Next.js
Write-Host "Membersihkan cache Next.js..." -ForegroundColor Green

# Pastikan tidak ada proses yang menggunakan folder .next
# Hapus folder .next jika ada
if (Test-Path -Path ".next") {
    Write-Host "Menghapus folder .next..." -ForegroundColor Yellow
    Remove-Item -Path ".next" -Recurse -Force -ErrorAction SilentlyContinue
}

# Hapus folder node_modules/.cache jika ada
if (Test-Path -Path "node_modules\.cache") {
    Write-Host "Menghapus folder node_modules\.cache..." -ForegroundColor Yellow
    Remove-Item -Path "node_modules\.cache" -Recurse -Force -ErrorAction SilentlyContinue
}

# Clear npm cache
Write-Host "Membersihkan npm cache..." -ForegroundColor Yellow
npm cache clean --force

Write-Host "Pembersihan selesai!" -ForegroundColor Green
Write-Host "Jalankan 'npm run dev' untuk memulai server development." -ForegroundColor Cyan
