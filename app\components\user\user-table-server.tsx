import { prisma } from "@/lib/config/prisma";
import { UserTable } from "./user-table";

export async function UserTableServer() {
  // Fetch users data
  const users = await prisma.user.findMany({
    select: {
      id: true,
      name: true,
      email: true,
      role: true,
      phone: true,
      image: true,
      createdAt: true,
      updatedAt: true,
    },
    orderBy: {
      createdAt: 'desc'
    }
  });

  return (
    <UserTable 
      searchQuery="" 
      users={users} 
    />
  );
} 
