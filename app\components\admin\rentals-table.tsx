"use client";

import React, { useState, useMemo } from "react";
import { formatCurrency } from "@/lib/utils/format";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table";
import { Badge } from "@/app/components/ui/badge";
import { Button } from "@/app/components/ui/button";
import { ModernTableScroll } from "@/app/components/ui/modern-table-scroll";
import { Pagination, usePagination } from "@/app/components/ui/pagination";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/app/components/ui/dropdown-menu";
import { LuMoreHorizontal, LuEye, LuFileText } from "react-icons/lu";
import Link from "next/link";

type RentalWithRelations = {
  id: string;
  startDate: Date;
  endDate: Date;
  operationalStart: Date | null;
  operationalEnd: Date | null;
  address: string;
  amount: number;
  status: string;
  createdAt: Date;
  user: {
    id: string;
    name: string;
    email: string;
    phone: string;
  };
  product: {
    id: string;
    name: string;
    capacity: number;
  };
  payment: {
    id: string;
    status: string;
  } | null;
};

interface RentalsTableProps {
  rentals: RentalWithRelations[];
  searchQuery?: string;
}

// Fungsi untuk mendapatkan tampilan status rental
function getRentalStatusBadge(status: string) {
  switch (status.toUpperCase()) {
    case "CONFIRMED":
      return { label: "Dikonfirmasi", color: "bg-blue-100 text-blue-800" };
    case "ONGOING":
      return { label: "Berlangsung", color: "bg-green-100 text-green-800" };
    case "COMPLETED":
      return { label: "Selesai", color: "bg-gray-100 text-gray-800" };
    case "CANCELLED":
      return { label: "Dibatalkan", color: "bg-red-100 text-red-800" };
    default:
      return { label: "Menunggu", color: "bg-yellow-100 text-yellow-800" };
  }
}

export function RentalsTable({ rentals, searchQuery = "" }: RentalsTableProps) {
  // Filter rentals based on search
  const filteredRentals = useMemo(() => {
    if (!searchQuery) return rentals;

    return rentals.filter(rental =>
      rental.user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      rental.user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      rental.user.phone.toLowerCase().includes(searchQuery.toLowerCase()) ||
      rental.product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      rental.address.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [rentals, searchQuery]);

  // Pagination
  const {
    currentPage,
    totalPages,
    startIndex,
    endIndex,
    goToPage,
    resetPage
  } = usePagination(filteredRentals.length, 5);

  // Reset page when search changes
  React.useEffect(() => {
    resetPage();
  }, [searchQuery, resetPage]);

  const paginatedRentals = useMemo(() => {
    return filteredRentals.slice(startIndex, endIndex);
  }, [filteredRentals, startIndex, endIndex]);

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('id-ID', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    }).format(new Date(date));
  };

  const formatDateTime = (date: Date | null) => {
    if (!date) return '-';
    return new Intl.DateTimeFormat('id-ID', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date));
  };

  const calculateDuration = (start: Date, end: Date) => {
    const diffTime = Math.abs(new Date(end).getTime() - new Date(start).getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return `${diffDays} hari`;
  };

  return (
    <div className="space-y-6">
      <Card className="border-border shadow-sm">
        <CardHeader className="border-b border-border bg-muted/50">
          <CardTitle className="text-foreground">Daftar Rental</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <ModernTableScroll>
            <Table>
              <TableHeader>
                <TableRow className="border-b border-border bg-muted/30 hover:bg-muted/50">
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">No</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Produk</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Pelanggan</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Tanggal Mulai</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Waktu Kedatangan</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Durasi</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Alamat</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Status</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Total</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium w-[60px]">Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedRentals.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={10} className="text-center py-12 text-muted-foreground">
                      {searchQuery ? `Tidak ada hasil untuk "${searchQuery}"` : "Tidak ada data rental"}
                    </TableCell>
                  </TableRow>
                ) : (
                  paginatedRentals.map((rental, index) => {
                    const statusBadge = getRentalStatusBadge(rental.status);
                    const globalIndex = startIndex + index + 1;

                    return (
                      <TableRow
                        key={rental.id}
                        className="border-b border-border hover:bg-muted/50 transition-colors"
                      >
                        <TableCell className="px-6 py-4 font-medium text-foreground">
                          {globalIndex}
                        </TableCell>
                        <TableCell className="px-6 py-4">
                          <div className="min-w-0">
                            <div className="font-medium text-foreground truncate">
                              {rental.product.name}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {rental.product.capacity} KVA
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="px-6 py-4">
                          <div className="min-w-0">
                            <div className="font-medium text-foreground truncate">
                              {rental.user.name}
                            </div>
                            <div className="text-xs text-muted-foreground font-mono">
                              {rental.user.phone}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="px-6 py-4 text-foreground">
                          {formatDate(rental.startDate)}
                        </TableCell>
                        <TableCell className="px-6 py-4 text-foreground">
                          {formatDateTime(rental.operationalStart)}
                        </TableCell>
                        <TableCell className="px-6 py-4 text-foreground">
                          {calculateDuration(rental.startDate, rental.endDate)}
                        </TableCell>
                        <TableCell className="px-6 py-4">
                          <div className="max-w-[200px] truncate text-foreground" title={rental.address}>
                            {rental.address}
                          </div>
                        </TableCell>
                        <TableCell className="px-6 py-4">
                          <Badge className={statusBadge.color}>
                            {statusBadge.label}
                          </Badge>
                        </TableCell>
                        <TableCell className="px-6 py-4 font-medium text-foreground">
                          {formatCurrency(rental.amount)}
                        </TableCell>
                        <TableCell className="px-6 py-4">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Buka menu</span>
                                <LuMoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem asChild>
                                <Link href={`/admin/rentals/${rental.id}`}>
                                  <LuEye className="mr-2 h-4 w-4" />
                                  Lihat Detail
                                </Link>
                              </DropdownMenuItem>
                              {rental.payment && (
                                <DropdownMenuItem asChild>
                                  <Link href={`/admin/payments/${rental.payment.id}`}>
                                    <LuFileText className="mr-2 h-4 w-4" />
                                    Lihat Invoice
                                  </Link>
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </ModernTableScroll>
        </CardContent>
      </Card>

      {/* Pagination */}
      {filteredRentals.length > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={filteredRentals.length}
          itemsPerPage={5}
          onPageChange={goToPage}
          className="mt-6"
        />
      )}
    </div>
  );
}
